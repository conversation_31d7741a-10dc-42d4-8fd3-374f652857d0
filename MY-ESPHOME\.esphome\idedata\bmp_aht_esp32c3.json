{"build_type": "release", "env_name": "bmp_aht_esp32c3", "libsource_dirs": ["C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3\\lib", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3\\.piolibdeps\\bmp_aht_esp32c3", "C:\\Users\\<USER>\\.platformio\\lib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries"], "defines": ["PLATFORMIO=60118", "ARDUINO_AirM2M_CORE_ESP32C3", "ARDUINO_USB_MODE=1", "ARDUINO_USB_CDC_ON_BOOT=1", "ARDUINO_USB_MODE=1", "ESPHOME_LOG_LEVEL=ESPHOME_LOG_LEVEL_DEBUG", "USE_ARDUINO", "USE_ESP32", "USE_ESP32_FRAMEWORK_ARDUINO", "USE_ESP32_VARIANT_ESP32C3", "ESP32_ARDUINO_LIB_BUILDER", "ESP_PLATFORM", "IDF_VER=\"v5.3.2-584-g489d7a2b3a-dirty\"", "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"", "MD5_ENABLED=1", "SERIAL_FLASHER_BOOT_HOLD_TIME_MS=50", "SERIAL_FLASHER_RESET_HOLD_TIME_MS=100", "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE", "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ", "UNITY_INCLUDE_CONFIG_H", "_GLIBCXX_HAVE_POSIX_SEMAPHORE", "_GLIBCXX_USE_POSIX_SEMAPHORE", "_GNU_SOURCE", "_POSIX_READER_WRITER_LOCKS", "TF_LITE_STATIC_MEMORY", "CHIP_CONFIG_SOFTWARE_VERSION_NUMBER=0", "CHIP_MINMDNS_DEFAULT_POLICY=1", "CHIP_MINMDNS_USE_EPHEMERAL_UNICAST_PORT=0", "CHIP_MINMDNS_HIGH_VERBOSITY=0", "CHIP_DNSSD_DEFAULT_MINIMAL=1", "ARDUINO_ARCH_ESP32", "CHIP_HAVE_CONFIG_H", "ESP32=ESP32", "F_CPU=160000000L", "ARDUINO=10812", "ARDUINO_VARIANT=\"AirM2M_CORE_ESP32C3\"", "ARDUINO_BOARD=\"AirM2M CORE ESP32C3\"", "ARDUINO_PARTITION_partitions"], "includes": {"build": ["C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Wire\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Update\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESPmDNS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFi\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Network\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\newlib\\platform_include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\freertos\\config\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\freertos\\config\\include\\freertos", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\freertos\\config\\riscv\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\freertos\\FreeRTOS-Kernel\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\freertos\\FreeRTOS-Kernel\\portable\\riscv\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\freertos\\FreeRTOS-Kernel\\portable\\riscv\\include\\freertos", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\freertos\\esp_additions\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hw_support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hw_support\\include\\soc", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hw_support\\include\\soc\\esp32c3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hw_support\\dma\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hw_support\\ldo\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hw_support\\port\\esp32c3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hw_support\\port\\esp32c3\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\heap\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\log\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\soc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\soc\\esp32c3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\soc\\esp32c3\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\hal\\platform_port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\hal\\esp32c3\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\hal\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_rom\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_rom\\include\\esp32c3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_rom\\esp32c3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_system\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_system\\port\\soc", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_system\\port\\include\\riscv", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_system\\port\\include\\private", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\riscv\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_timer\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\include\\apps", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\include\\apps\\sntp", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\lwip\\src\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\port\\freertos\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\port\\esp32xx\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\port\\esp32xx\\include\\arch", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\lwip\\port\\esp32xx\\include\\sys", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-tflite-micro", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-tflite-micro\\third_party\\gemmlowp", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-tflite-micro\\third_party\\flatbuffers\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-tflite-micro\\third_party\\ruy", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-tflite-micro\\third_party\\kissfft", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\lib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\lib\\dnssd", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\platform\\OpenThread", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\third_party\\nlfaultinjection\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\third_party\\nlassert\\repo\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\third_party\\nlio\\repo\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\zzz_generated\\app-common", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp-idf\\espressif__esp_matter", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_matter", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_matter\\utils", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_matter_bridge", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_matter_console", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_matter\\zap_common", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\platform\\ESP32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\platform\\ESP32\\bluedroid", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\platform\\ESP32\\nimble", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_matter\\connectedhomeip\\connectedhomeip\\src\\platform\\ESP32\\route_hook", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_eth\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_event\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_spi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_pm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\include\\esp32c3\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\common\\osi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\common\\api\\include\\api", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\common\\btc\\profile\\esp\\blufi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\common\\btc\\profile\\esp\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\common\\hci_log\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\host\\bluedroid\\api\\include\\api", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\common\\tinycrypt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\core", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\core\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\core\\storage", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\btc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\models\\common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\models\\client\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\models\\server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\api\\core\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\api\\models\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\api", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\lib\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\v1.1\\api\\core\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\v1.1\\api\\models\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\esp_ble_mesh\\v1.1\\btc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bt\\porting\\ext\\tinycrypt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_wifi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_wifi\\include\\local", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_wifi\\wifi_apps\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_wifi\\wifi_apps\\nan_app\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_phy\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_phy\\esp32c3\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_netif\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mbedtls\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mbedtls\\mbedtls\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mbedtls\\mbedtls\\library", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mbedtls\\esp_crt_bundle\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mbedtls\\mbedtls\\3rdparty\\everest\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mbedtls\\mbedtls\\3rdparty\\p256-m", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mbedtls\\mbedtls\\3rdparty\\p256-m\\p256-m", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\fatfs\\diskio", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\fatfs\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\fatfs\\vfs", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\wear_levelling\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_partition\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\sdmmc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_sdmmc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_gpio\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_sdspi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\app_update\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bootloader_support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\bootloader_support\\bootloader_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_app_format\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_bootloader_format\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\console", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\vfs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_vfs_console\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_uart\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\driver\\deprecated", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\driver\\i2c\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\driver\\touch_sensor\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\driver\\twai\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_ringbuf\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_pcnt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_gptimer\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_mcpwm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_ana_cmpr\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_i2s\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_sdio\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_dac\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_rmt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_tsens\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_sdm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_i2c\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_ledc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_parlio\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_usb_serial_jtag\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\nvs_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\spi_flash\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_secure_cert_mgr\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\efuse\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\efuse\\esp32c3\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__json_parser\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__jsmn\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\spiffs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_http_client\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__json_generator\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\json\\cJSON", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__mdns\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_encrypted_img\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_insights\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_diagnostics\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_mm\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\pthread\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\app_trace\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\wpa_supplicant\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\wpa_supplicant\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\wpa_supplicant\\esp_supplicant\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_coex\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\unity\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\unity\\unity\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\cmock\\CMock\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\http_parser", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp-tls", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp-tls\\esp-tls-crypto", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_adc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_adc\\interface", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_adc\\esp32c3\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_adc\\deprecated\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_isp\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_cam\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_cam\\interface", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_jpeg\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_driver_ppa\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_gdbstub\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_hid\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\tcp_transport\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_http_server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_https_ota\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_https_server\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_psram\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_lcd\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_lcd\\interface", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\protobuf-c\\protobuf-c", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\protocomm\\include\\common", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\protocomm\\include\\security", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\protocomm\\include\\transports", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\protocomm\\include\\crypto\\srp6a", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\protocomm\\proto-c", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\esp_local_ctrl\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espcoredump\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espcoredump\\include\\port\\riscv", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\idf_test\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\idf_test\\include\\esp32c3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\ieee802154\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\mqtt\\esp-mqtt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\nvs_sec_provider\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\wifi_provisioning\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-nn\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-nn\\src\\common", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__rmaker_common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__cbor\\port\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_diag_data_store\\src\\rtc_store", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_diag_data_store\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\chmorgan__esp-libhelix-mp3\\libhelix-mp3\\pub", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-modbus\\freemodbus\\common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__libsodium\\libsodium\\src\\libsodium\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__libsodium\\port_include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\dotprod\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\support\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\support\\mem\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\windows\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\windows\\hann\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\windows\\blackman\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\windows\\blackman_harris\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\windows\\blackman_nuttall\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\windows\\nuttall\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\windows\\flat_top\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\iir\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\fir\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\math\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\math\\add\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\math\\sub\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\math\\mul\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\math\\addc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\math\\mulc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\math\\sqrt\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\matrix\\mul\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\matrix\\add\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\matrix\\addc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\matrix\\mulc\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\matrix\\sub\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\matrix\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\fft\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\dct\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\conv\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\common\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\matrix\\mul\\test\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\kalman\\ekf\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-dsp\\modules\\kalman\\ekf_imu13states\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_modem\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_schedule\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__network_provisioning\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-serial-flasher\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp-serial-flasher\\port", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_rcp_update\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__esp_rainmaker\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\espressif__qrcode\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\joltwallet__littlefs\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\include\\fb_gfx\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32-libs\\esp32c3\\dio_qspi\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\cores\\esp32", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\variants\\AirM2M_CORE_ESP32C3"], "compatlib": ["C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESPmDNS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Network\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Update\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFi\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Wire\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ArduinoOTA\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\AsyncUDP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\BLE\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\BluetoothSerial\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\DNSServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\EEPROM\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESP32\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESP_I2S\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESP_NOW\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\ESP_SR\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Ethernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\FFat\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\FS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\HTTPClient\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\HTTPUpdate\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\HTTPUpdateServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Insights\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\LittleFS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Matter\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\NetBIOS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\NetworkClientSecure\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\OpenThread\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\PPP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Preferences\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\RainMaker\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SD\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SD_MMC\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SPI\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SPIFFS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\SimpleBLE\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\TFLiteMicro\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Ticker\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\USB\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WebServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\WiFiProv\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\libraries\\Zigbee\\src"], "toolchain": ["C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp@13.2.0+20240530\\riscv32-esp-elf\\include\\c++\\13.2.0", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp@13.2.0+20240530\\riscv32-esp-elf\\include\\c++\\13.2.0\\riscv32-esp-elf", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp@13.2.0+20240530\\lib\\gcc\\riscv32-esp-elf\\13.2.0\\include", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp@13.2.0+20240530\\lib\\gcc\\riscv32-esp-elf\\13.2.0\\include-fixed", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp@13.2.0+20240530\\riscv32-esp-elf\\include"]}, "cc_flags": ["-march=rv32imc_zicsr_zifencei", "-std=gnu17", "-Wno-old-style-declaration", "-Werror=return-type", "-Wno-sign-compare", "-Wno-unused-but-set-variable", "-Wno-unused-variable", "-fno-exceptions", "-<PERSON><PERSON>", "-ffunction-sections", "-fdata-sections", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-error=unused-but-set-variable", "-Wno-error=deprecated-declarations", "-Wno-unused-parameter", "-Wno-sign-compare", "-Wno-enum-conversion", "-gdwarf-4", "-ggdb", "-nostartfiles", "-freorder-blocks", "-Wwrite-strings", "-fstack-protector", "-fstrict-volatile-bitfields", "-fno-jump-tables", "-fno-tree-switch-conversion", "-Wno-error=uninitialized", "-Wno-error=maybe-uninitialized", "-Wno-format-nonliteral", "-Wno-missing-field-initializers", "-Wno-error=array-bounds", "-Wno-write-strings", "\"-DCHIP_ADDRESS_RESOLVE_IMPL_INCLUDE_HEADER=<lib/address_resolve/AddressResolve_DefaultImpl.h>\"", "-M<PERSON>"], "cxx_flags": ["-Wno-volatile", "-std=gnu++20", "-march=rv32imc_zicsr_zifencei", "-fexceptions", "-fno-rtti", "-Werror=return-type", "-Wno-sign-compare", "-Wno-unused-but-set-variable", "-Wno-unused-variable", "-fno-exceptions", "-<PERSON><PERSON>", "-ffunction-sections", "-fdata-sections", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-error=unused-but-set-variable", "-Wno-error=deprecated-declarations", "-Wno-unused-parameter", "-Wno-sign-compare", "-Wno-enum-conversion", "-gdwarf-4", "-ggdb", "-nostartfiles", "-freorder-blocks", "-Wwrite-strings", "-fstack-protector", "-fstrict-volatile-bitfields", "-fno-jump-tables", "-fno-tree-switch-conversion", "-Wno-error=uninitialized", "-Wno-error=maybe-uninitialized", "-Wno-format-nonliteral", "-Wno-missing-field-initializers", "-Wno-error=array-bounds", "-Wno-write-strings", "\"-DCHIP_ADDRESS_RESOLVE_IMPL_INCLUDE_HEADER=<lib/address_resolve/AddressResolve_DefaultImpl.h>\"", "-M<PERSON>"], "cc_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp@13.2.0+20240530\\bin\\riscv32-esp-elf-gcc.exe", "cxx_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-riscv32-esp@13.2.0+20240530\\bin\\riscv32-esp-elf-g++.exe", "gdb_path": "C:\\Users\\<USER>\\.platformio\\packages\\tool-riscv32-esp-elf-gdb\\bin\\riscv32-esp-elf-gdb.exe", "prog_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3\\.pioenvs\\bmp_aht_esp32c3\\firmware.elf", "svd_path": null, "compiler_type": "gcc", "targets": [{"name": "buildfs", "title": "Build Filesystem Image", "description": null, "group": "Platform"}, {"name": "size", "title": "Program Size", "description": "Calculate program size", "group": "Platform"}, {"name": "upload", "title": "Upload", "description": null, "group": "Platform"}, {"name": "uploadfs", "title": "Upload Filesystem Image", "description": null, "group": "Platform"}, {"name": "uploadfsota", "title": "Upload Filesystem Image OTA", "description": null, "group": "Platform"}, {"name": "erase_upload", "title": "Erase Flash and Upload", "description": null, "group": "Platform"}, {"name": "erase", "title": "Erase Flash", "description": null, "group": "Platform"}], "extra": {"flash_images": [{"offset": "0x0000", "path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3\\.pioenvs\\bmp_aht_esp32c3\\bootloader.bin"}, {"offset": "0x8000", "path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3\\.pioenvs\\bmp_aht_esp32c3\\partitions.bin"}, {"offset": "0xe000", "path": "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif32\\tools\\partitions\\boot_app0.bin"}]}}